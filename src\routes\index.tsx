import { AuthenticatedLayout, MainLayout } from '@/components';
import { Navigate, useRoutes } from 'react-router-dom';
import { appRoutes } from './app.routes';
import { authRoutes } from './auth.routes';
import { generalRoutes } from './general.routes';

export const Router = () =>
  useRoutes([
    {
      path: '/',
      element: <Navigate to="auth" replace />,
      index: true
    },
    {
      path: '/auth',
      children: authRoutes,
      element: <MainLayout />
    },
    {
      path: '/app',
      children: appRoutes,
      element: <AuthenticatedLayout />
    },
    {
      path: '*',
      children: generalRoutes,
      element: <MainLayout />
    },
    { path: '*', element: <Navigate to="/404" replace /> },
  ]);
