import type { CreateAgentInputInterface, EditAgentInputInterface, IAgent } from "@/interfaces/IAgents";
import type { IResponse } from "@/interfaces/IResponse";
import { api } from "@/utils/api";
import type { AxiosInstance } from "axios";


class AgentsService {
    private readonly api: AxiosInstance;
    private readonly endpoint = '/Agent';

    constructor(api: AxiosInstance) {
        this.api = api;
    }

    async create(params: CreateAgentInputInterface): Promise<IResponse<IAgent>> {
        const response = await this.api.post(`${this.endpoint}`, params);
        return response.data;
    }

    async getAll(userId: number): Promise<IAgent[]> {
        const response = await this.api.get(`${this.endpoint}/userId/${userId}`);
        return response.data;
    }

    async update(id: number, params: EditAgentInputInterface): Promise<IResponse<IAgent>> {
        const response = await this.api.put(`${this.endpoint}/${id}`, params);
        return response.data;
    }

    async delete(id: number): Promise<void> {
        await this.api.delete(`${this.endpoint}/${id}`);
    }

}

const useAgentService = new AgentsService(api);

export { AgentsService, useAgentService };
