name: Preview Build

on:
  pull_request:
    branches: [ main ]
    types: [opened, synchronize, reopened, closed]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  PREVIEW_BUCKET_NAME: ${{ secrets.GCS_PREVIEW_BUCKET_NAME }}

jobs:
  preview-build:
    runs-on: ubuntu-latest
    if: github.event.action != 'closed'

    # Configurar permissões para Workload Identity
    permissions:
      contents: read
      id-token: write
      pull-requests: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Clean Cache
      run: npm cache clean --force

    - name: Delete Node Modules and Cache Files
      run: rm -rf node_modules package-lock.json pnpm-lock.yaml yarn.lock

    - name: Install Rollup Linux x64
      run: npm install @rollup/rollup-linux-x64-gnu

    - name: Install dependencies
      run: npm install

    - name: Run linter
      run: npm run lint

    - name: Build project
      run: npm run build
      env:
        CI: false

    - name: Authenticate to Google Cloud
      id: auth
      uses: google-github-actions/auth@v2
      with:
        workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
        service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}
    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Deploy preview to GCS
      run: |
        # Criar pasta única para o PR
        PR_FOLDER="pr-${{ github.event.number }}"

        # Upload dos arquivos buildados para pasta do PR
        gsutil -m cp -r dist/* gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/

        # Configurar cache headers
        gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/**/*.js
        gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/**/*.css
        gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/**/*.png
        gsutil -m setmeta -h "Cache-Control:no-cache" gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/**/*.html

    - name: Comment PR with preview URL
      uses: actions/github-script@v7
      with:
        script: |
          const prNumber = context.issue.number;
          const previewUrl = `https://storage.googleapis.com/${{ env.PREVIEW_BUCKET_NAME }}/pr-${prNumber}/index.html`;

          github.rest.issues.createComment({
            issue_number: prNumber,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `🚀 **Preview Deploy Ready!**

            📍 **Preview URL:** ${previewUrl}

            This preview will be available until the PR is merged or closed.`
          });

  cleanup-preview:
    runs-on: ubuntu-latest
    if: github.event.action == 'closed'

    # Configurar permissões para Workload Identity
    permissions:
      contents: read
      id-token: write

    steps:
    - name: Authenticate to Google Cloud
      id: auth
      uses: google-github-actions/auth@v2
      with:
        workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
        service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ secrets.GCP_PROJECT_ID }}

    - name: Cleanup preview files
      run: |
        PR_FOLDER="pr-${{ github.event.number }}"
        gsutil -m rm -r gs://${{ secrets.GCS_PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/ || true
        echo "🧹 Preview files cleaned up for PR #${{ github.event.number }}"
