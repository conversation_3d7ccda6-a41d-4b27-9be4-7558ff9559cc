import type { FieldConfig } from "@/interfaces/ICustomForm";

export const createAgentInputs: FieldConfig[] = [
    {
        name: "name",
        label: "Nome",
        type: "text",
        placeholder: "Qual será o nome do agente?",
    },
    {
        name: "context",
        label: "Descri<PERSON>",
        type: "textarea",
        placeholder: "Descreva brevemente o agente",
    }
]

export const paramsAgentInputs: FieldConfig[] = [
    {
        name: "paramName",
        label: "Nome do Parâmetro *",
        type: "text",
        placeholder: "Digite o nome do parâmetro",
        inputClassName: "w-full h-10 px-3 border rounded",
        wrapperClassName: "w-full",
    },
    {
        name: "companyName",
        label: "Nome da Empresa",
        type: "text",
        placeholder: "Digite o nome da empresa",
        inputClassName: "w-full h-10 px-3 border rounded",
        wrapperClassName: "w-full",
    },
    {
        name: "companyDescription",
        label: "Descri<PERSON> da Empresa",
        type: "textarea",
        placeholder: "Descreva brevemente a empresa",
    },
    {
        name: "contextDescription",
        label: "Contexto",
        type: "textarea",
        placeholder: "Explique o contexto em que o agente atuará",
    },
    {
        name: "conversationGuidelines",
        label: "Diretrizes de Conversa",
        type: "textarea",
        placeholder: "Defina regras de como o agente deve se comunicar",
    },
    {
        name: "goals",
        label: "Objetivos",
        type: "textarea",
        placeholder: "Liste os objetivos principais do agente",
    },
    {
        name: "mainMission",
        label: "Missão Principal",
        type: "textarea",
        placeholder: "Qual é a missão central do agente?",
    },
    {
        name: "openingScript",
        label: "Roteiro de Abertura",
        type: "textarea",
        placeholder: "Escreva o script de abertura da conversa",
    },
    {
        name: "painAgitationScript",
        label: "Roteiro de Agitação de Dor",
        type: "textarea",
        placeholder: "Como o agente deve reforçar os pontos de dor?",
    },
    {
        name: "preQualificationQuestions",
        label: "Perguntas de Pré-Qualificação",
        type: "textarea",
        placeholder: "Liste perguntas para pré-qualificar leads",
    },
    {
        name: "pricingAgitationScript",
        label: "Roteiro de Agitação de Preço",
        type: "textarea",
        placeholder: "Como o agente deve tratar objeções sobre preço?",
    },
    {
        name: "qualificationRules",
        label: "Regras de Qualificação",
        type: "textarea",
        placeholder: "Defina os critérios de qualificação",
    },
    {
        name: "tone",
        label: "Tom de Comunicação",
        type: "text",
        placeholder: "Ex: Formal, Informal, Amigável",
    },
    {
        name: "alternativeSuggestionStyle",
        label: "Estilo de Sugestão Alternativa",
        type: "textarea",
        placeholder: "Como sugerir alternativas ao usuário?",
    },
    {
        name: "askAvailabilityStyle",
        label: "Estilo de Pergunta de Disponibilidade",
        type: "textarea",
        placeholder: "Como o agente deve perguntar sobre disponibilidade?",
    },
    {
        name: "callToAction",
        label: "Chamada para Ação (CTA)",
        type: "text",
        placeholder: "Ex: Agende sua demonstração agora",
    },
    {
        name: "callToActionScript",
        label: "Roteiro de CTA",
        type: "textarea",
        placeholder: "Escreva como o agente deve conduzir o CTA",
    },
    {
        name: "confirmationStyle",
        label: "Estilo de Confirmação",
        type: "textarea",
        placeholder: "Como confirmar informações com o usuário?",
    },
    {
        name: "courtesyMessage",
        label: "Mensagem de Cortesia",
        type: "textarea",
        placeholder: "Defina uma mensagem educada/padrão",
    },
    {
        name: "disqualifiedFlowScript",
        label: "Fluxo para Desqualificados",
        type: "textarea",
        placeholder: "Escreva como lidar com leads desqualificados",
    },
    {
        name: "emotionalActivationScript",
        label: "Roteiro de Ativação Emocional",
        type: "textarea",
        placeholder: "Como ativar emoções no usuário?",
    },
    {
        name: "finalQualificationQuestions",
        label: "Perguntas Finais de Qualificação",
        type: "textarea",
        placeholder: "Perguntas para validar o lead antes da passagem",
    },
    {
        name: "opportunityReinforcementScript",
        label: "Roteiro de Reforço da Oportunidade",
        type: "textarea",
        placeholder: "Como reforçar a importância da solução?",
    },
    {
        name: "recurrenceStyle",
        label: "Estilo de Recorrência",
        type: "textarea",
        placeholder: "Como abordar a continuidade/recorrência?",
    },
    {
        name: "reminderStyle",
        label: "Estilo de Lembrete",
        type: "textarea",
        placeholder: "Defina como enviar lembretes",
    },
    {
        name: "reminderTiming",
        label: "Tempo do Lembrete",
        type: "text",
        placeholder: "Ex: 24h antes, 1h antes",
    },
    {
        name: "restrictionsAndLimits",
        label: "Restrições e Limites",
        type: "textarea",
        placeholder: "Liste restrições do agente",
    },
    {
        name: "solutionScript",
        label: "Roteiro da Solução",
        type: "textarea",
        placeholder: "Explique como apresentar a solução",
    },
    {
        name: "traditionalMethods",
        label: "Métodos Tradicionais",
        type: "textarea",
        placeholder: "Liste métodos tradicionais a serem contrastados",
    },
    {
        name: "userTone",
        label: "Tom do Usuário",
        type: "text",
        placeholder: "Ex: Empolgado, Objetivo, Curioso",
    },
    {
        name: "valueGenerationScript",
        label: "Roteiro de Geração de Valor",
        type: "textarea",
        placeholder: "Escreva como o agente deve demonstrar valor",
    },
];