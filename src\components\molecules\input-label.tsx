import { Input, Label } from '../atoms';

interface InputLabelProps extends React.ComponentProps<'input'> {
  label: string;
}

export const InputLabel = ({ label, className, ...rest }: InputLabelProps) => (
  <div className={`flex flex-col gap-2 ${className}`}>
    <Label className="text-zinc-800 text-sm font-medium">{label}</Label>
    <Input className="border-gray-200 h-10 sm:h-12 text-sm sm:text-base" {...rest} />
  </div>
);
