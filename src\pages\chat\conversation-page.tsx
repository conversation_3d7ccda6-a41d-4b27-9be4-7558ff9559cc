import { Button } from '@/components';
import { ChatInterface } from '@/components/organisms';
import PageHeader from '@/components/organisms/page-header';
import { ArrowLeftIcon, MessageCircleIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

function ConversationChatPage() {
  const navigate = useNavigate();

  return (
    <main className="max-h-[500px] container mx-auto flex flex-col">
      <PageHeader
        title="Conversa"
        description="Gerencie suas conversas de forma eficaz."
        icon={<MessageCircleIcon size={30} className="text-blue-500" />}
      />

      <section className='mb-4'>
        <Button
          className="p-4 mt-4 flex items-center cursor-pointer hover:bg-gray-50"
          variant="secondary"
          onClick={() => navigate(-1)}
        >
          <ArrowLeftIcon className="inline-block mr-2" size={16} />
          <span className="text-sm font-semibold">Voltar</span>
        </Button>
      </section>

      {/* Chat Interface - Container principal e secundário */}
      <section className="max-h-[88%] min-h-0 mb-0">
        <ChatInterface />
      </section>
    </main>
  );
}
export default ConversationChatPage;
