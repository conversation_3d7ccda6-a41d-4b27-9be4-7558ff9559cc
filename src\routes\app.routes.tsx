import AgentsPage from '@/pages/agents/agents-page';
import ChatPage from '@/pages/chat';
import ConversationChatPage from '@/pages/chat/conversation-page';
import DashboardPage from '@/pages/core/dahsboard-page';
import { Navigate, type RouteObject } from 'react-router-dom';

export const appRoutes: RouteObject[] = [
  {
    path: '',
    element: <Navigate to="dashboard" replace />,
  },
  {
    path: 'dashboard',
    element: <DashboardPage />,
  },
  {
    path: 'agents',
    element: <AgentsPage />,
  },
  {
    path: 'chats',
    element: <ChatPage />,
  },
  {
    path: 'chats/conversation/:id',
    element: <ConversationChatPage />,
  },
];