import React from 'react';
import ConversationList from './conversation-list';
import MessageList from './message-list';
import MessageInput from './message-input';
import useConversation from '@/hooks/useConversation';
import { Card } from '@/components';
import { AlertCircleIcon } from 'lucide-react';
import { useParams } from 'react-router-dom';

const ChatInterface: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const {
    loading,
    conversations,
    selectedConversation,
    messages,
    newMessage,
    sendingMessage,
    messagesEndRef,
    setNewMessage,
    selectConversation,
  } = useConversation();

  // Verificar se o agente está conectado

  if (loading && !id) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-gray-600">Carregando dados do agente...</p>
        </div>
      </div>
    );
  }

  if (!id) {
    return (
      <Card className="h-full flex items-center justify-center">
        <div className="text-center p-8">
          <AlertCircleIcon size={64} className="text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Agente não encontrado
          </h3>
          <p className="text-gray-500">
            Não foi possível carregar os dados do agente selecionado.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="h-full flex bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Lista de Conversas - Container Principal */}
      <div className="w-80 flex-shrink-0 border-r border-gray-200">
        <ConversationList
          conversations={conversations}
          selectedConversation={selectedConversation}
          onSelectConversation={selectConversation}
          loading={loading}
        />
      </div>

      {/* Área de Mensagens - Container Secundário */}
      <div className="flex-1 flex flex-col">
        {/* Lista de Mensagens */}
        <MessageList
          messages={messages}
          selectedConversation={selectedConversation}
          messagesEndRef={messagesEndRef}
          loading={loading}
        />

        {/* Input de Mensagem */}
        {selectedConversation && (
          <MessageInput
            value={newMessage}
            onChange={setNewMessage}
            onSend={() => {}}
            disabled
            loading={sendingMessage}
            placeholder={`Comming Son...`}
          />
        )}
      </div>
    </div>
  );
};

export default ChatInterface;
