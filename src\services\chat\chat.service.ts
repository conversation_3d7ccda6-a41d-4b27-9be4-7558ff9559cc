import type { CheckConnectionStatusOutputInterface, GenerateQrCodeInputInterface, GenerateQrCodeOutputInterface, GetStatusInstanceInputInterface, SendMessageInputInterface } from "@/interfaces/IChat";
import { messagesApi } from "@/utils/api";
import type { AxiosInstance } from "axios";


class ChatService {
    private readonly api: AxiosInstance;
    private readonly basePath = '/WhatsApp';


    constructor(api: AxiosInstance) {
        this.api = api;
    }


    async generateQrCode(params: GenerateQrCodeInputInterface): Promise<GenerateQrCodeOutputInterface> {
        try {
            const response = await this.api.get(`${this.basePath}/${params.instanceName}/qrcode`);
            return response.data;
        } catch (error) {
            console.error('Erro ao criar produto:', error);
            throw error;
        }
    }

    async getStatusInstance(params: GetStatusInstanceInputInterface): Promise<CheckConnectionStatusOutputInterface> {
        try {
            const response = await this.api.get(`${this.basePath}/${params.instanceName}/connection-status`);
            return response.data;
        } catch (error) {
            console.error('Erro ao obter status da instância:', error);
            throw error;
        }
    }

    async sendMessage(params: SendMessageInputInterface): Promise<void> {
        try {
            const response = await this.api.post(`${this.basePath}/${params.instanceName}/send-message`, {
                number: params.number,
                text: params.text,
                delay: params.delay
            });
            return response.data;
        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            throw error;
        }
    }

}

const useChatService = new ChatService(messagesApi);

export { ChatService, useChatService };
