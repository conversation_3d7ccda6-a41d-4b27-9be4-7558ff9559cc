import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import type { IMessage, IConversation } from '@/interfaces/IChat';
import { toast } from 'sonner';
import axios from 'axios';

function useConversation() {
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [conversations, setConversations] = useState<IConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<IConversation | null>(null);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Função para rolar para o final das mensagens
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const getAllConversationByAgentId = async (agentId: number): Promise<IConversation[]> => {
    try {
      setLoading(true);

      const response = await axios.get(`https://localhost:5001/api/v1/Message/agent/${agentId}/conversations`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar conversas:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const getAllMessagesByConversationId = async (conversationId: string): Promise<IMessage[]> => {
    try {
      setLoading(true);

      const response = await axios.get(`https://localhost:5001/api/v1/Message/conversation/${conversationId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Carregar conversas
  const loadConversations = useCallback(async () => {
    try {
      if (!id) return;
      setLoading(true);
      // Por enquanto usando dados mockados
      // TODO: Implementar chamada real da API quando disponível
      const conversations = await getAllConversationByAgentId(+id);
      setConversations(conversations);

      // Selecionar a primeira conversa por padrão
      if (conversations.length > 0) {
        setSelectedConversation(conversations[0]);
      }
    } catch (error) {
      console.error('Erro ao carregar conversas:', error);
      toast.error('Erro ao carregar conversas');
    } finally {
      setLoading(false);
    }
  }, []);

  // Carregar mensagens de uma conversa
  const loadMessages = useCallback(async (conversation: IConversation) => {
    try {
      setLoading(true);
      console.log(conversation.conversationIndetification);

      const messages = await getAllMessagesByConversationId(conversation.conversationIndetification);
      setMessages(messages);
    } catch (error) {
      console.error('Erro ao carregar mensagens:', error);
      toast.error('Erro ao carregar mensagens');
    } finally {
      setLoading(false);
    }
  }, []);

  // Selecionar conversa
  const selectConversation = useCallback((conversation: IConversation) => {
    setSelectedConversation(conversation);
    loadMessages(conversation);
  }, [loadMessages]);



  useEffect(() => {
    if (id) {
      loadConversations();
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  return {
    loading,
    conversations,
    selectedConversation,
    messages,
    newMessage,
    sendingMessage,
    messagesEndRef,
    setNewMessage,
    selectConversation,
    scrollToBottom,
  };
}

export default useConversation;
