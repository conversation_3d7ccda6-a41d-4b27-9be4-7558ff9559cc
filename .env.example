# Exemplo de variáveis de ambiente para desenvolvimento
# Copie este arquivo para .env.local e preencha com seus valores

# Configurações da aplicação
VITE_APP_NAME="Frontend Template"
VITE_APP_VERSION="1.0.0"

# URLs da API (desenvolvimento)
VITE_API_URL="http://localhost:3000/api"
VITE_API_VERSION="v1"

# Configurações de autenticação
VITE_AUTH_DOMAIN="your-auth-domain.com"
VITE_AUTH_CLIENT_ID="your-client-id"

# Configurações do Google Analytics (opcional)
VITE_GA_TRACKING_ID="GA-XXXXXXXXX"

# Configurações de debug
VITE_DEBUG_MODE="true"
VITE_LOG_LEVEL="debug"

# ========================================
# CONFIGURAÇÕES DE DEPLOY (NÃO COMMITAR)
# ========================================
# Estas variáveis devem ser configuradas apenas nos GitHub Secrets
# Não adicione valores reais aqui!

# GCP_PROJECT_ID=seu-project-id
# GCS_BUCKET_NAME=seu-bucket-producao
# GCS_PREVIEW_BUCKET_NAME=seu-bucket-preview
# GCP_SA_KEY=sua-chave-service-account-base64
