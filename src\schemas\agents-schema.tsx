import { z } from "zod";

const CreateAgentSchema = z.object({
    name: z.string().min(1, "Campo obrigatório"),
    context: z.string().min(1, "Campo obrigatório"),
    agentParamsId: z.number().optional().nullable()
})

const UpdateAgentSchema = CreateAgentSchema.extend({
    id: z.string().uuid(),
})

export const CreateAgentParamsSchema = z.object({
  paramName: z.string().min(1, "Campo obrigatório"),
  companyDescription: z.string().nullable().optional(),
  companyName: z.string().nullable().optional(),
  contextDescription: z.string().nullable().optional(),
  conversationGuidelines: z.string().nullable().optional(),
  goals: z.string().nullable().optional(),
  mainMission: z.string().nullable().optional(),
  openingScript: z.string().nullable().optional(),
  painAgitationScript: z.string().nullable().optional(),
  preQualificationQuestions: z.string().nullable().optional(),
  pricingAgitationScript: z.string().nullable().optional(),
  qualificationRules: z.string().nullable().optional(),
  tone: z.string().nullable().optional(),
  alternativeSuggestionStyle: z.string().nullable().optional(),
  askAvailabilityStyle: z.string().nullable().optional(),
  callToAction: z.string().nullable().optional(),
  callToActionScript: z.string().nullable().optional(),
  confirmationStyle: z.string().nullable().optional(),
  courtesyMessage: z.string().nullable().optional(),
  disqualifiedFlowScript: z.string().nullable().optional(),
  emotionalActivationScript: z.string().nullable().optional(),
  finalQualificationQuestions: z.string().nullable().optional(),
  opportunityReinforcementScript: z.string().nullable().optional(),
  recurrenceStyle: z.string().nullable().optional(),
  reminderStyle: z.string().nullable().optional(),
  reminderTiming: z.string().nullable().optional(),
  restrictionsAndLimits: z.string().nullable().optional(),
  solutionScript: z.string().nullable().optional(),
  traditionalMethods: z.string().nullable().optional(),
  userId: z.number().nullable().optional(),
  userTone: z.string().nullable().optional(),
  valueGenerationScript: z.string().nullable().optional(),
});

export type CreateAgentParamsInput = z.infer<typeof CreateAgentParamsSchema>;
export type CreateAgentInput = z.infer<typeof CreateAgentSchema>
export type UpdateAgentInput = z.infer<typeof UpdateAgentSchema>

export { CreateAgentSchema, UpdateAgentSchema };

