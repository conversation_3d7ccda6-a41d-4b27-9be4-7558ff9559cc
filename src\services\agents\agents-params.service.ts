import type { IAgent, IAgentParams, SearchAgentInputInterface } from "@/interfaces/IAgents";
import type { IResponse } from "@/interfaces/IResponse";
import { api } from "@/utils/api";
import type { AxiosInstance } from "axios";

type IAgentParamsWithoutId = Omit<IAgentParams, 'id'>;
class AgentsParamsService {
    private readonly api: AxiosInstance;
    private readonly endpoint = '/AgentParams';

    constructor(api: AxiosInstance) {
        this.api = api;
    }

    async create(params: IAgentParamsWithoutId): Promise<IResponse<IAgentParams>> {
        const response = await this.api.post(`${this.endpoint}`, params);
        return response.data;
    }

    async getByUserId(userId: number): Promise<IAgentParams[]> {
        const response = await this.api.get(`${this.endpoint}/user/${userId}`);
        return response.data;
    }

    async update(id: number, params: IAgentParams): Promise<IResponse<IAgentParams>> {
        const response = await this.api.put(`${this.endpoint}/${id}`, params);
        return response.data;
    }

    async delete(id: number): Promise<void> {
        await this.api.delete(`${this.endpoint}/${id}`);
    }

    async searchByName(params: SearchAgentInputInterface): Promise<IAgent[]> {
        const response = await this.api.get(`${this.endpoint}/search`,
            { params },
        );
        return response.data;
    }

    async getById(id: number): Promise<IAgentParams> {
        const response = await this.api.get(`${this.endpoint}/${id}`);
        return response.data;
    }
}

const useAgentsParamsService = new AgentsParamsService(api);

export { AgentsParamsService, useAgentsParamsService };
