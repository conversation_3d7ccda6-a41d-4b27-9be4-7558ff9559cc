# Componente de Chat

Este diretório contém os componentes responsáveis pela interface de chat da aplicação.

## Estrutura dos Componentes

### ChatInterface
Componente principal que orquestra toda a interface de chat. Combina os demais componentes e gerencia o estado global do chat.

**Características:**
- Layout responsivo com dois containers (lista de conversas e área de mensagens)
- Verificação de conectividade do agente
- Estados de loading e erro
- Integração com o hook `useConversation`

### ConversationList
Container principal que exibe a lista de conversas disponíveis.

**Características:**
- Lista de conversas com busca
- Indicadores de mensagens não lidas
- Avatars dos contatos
- Timestamps formatados em português
- Scroll personalizado
- Seleção de conversa ativa

### MessageList
Container secundário que exibe as mensagens da conversa selecionada.

**Características:**
- Scroll invertido (mensagens mais recentes no final)
- Auto-scroll para novas mensagens
- Separadores de data
- Indicadores de status das mensagens (enviando, enviado, entregue, lido, falha)
- Diferenciação visual entre mensagens do usuário e do agente
- Formatação de timestamps

### MessageInput
Componente para entrada de novas mensagens.

**Características:**
- Textarea redimensionável
- Suporte a Shift+Enter para nova linha
- Contador de caracteres
- Estados de loading durante envio
- Validação de mensagem vazia

## Hook useConversation

Hook personalizado que gerencia todo o estado da conversa:

**Estados gerenciados:**
- `loading`: Estado de carregamento
- `agent`: Dados do agente atual
- `conversations`: Lista de conversas
- `selectedConversation`: Conversa selecionada
- `messages`: Mensagens da conversa atual
- `newMessage`: Texto da nova mensagem
- `sendingMessage`: Estado de envio de mensagem

**Funcionalidades:**
- Busca dados do agente por ID (da URL)
- Carrega conversas (atualmente mockadas)
- Carrega mensagens de uma conversa
- Envia mensagens via API
- Auto-scroll para novas mensagens
- Atualização de status das mensagens

## Interfaces

### IMessage
```typescript
interface IMessage {
  id: string;
  text: string;
  timestamp: Date;
  isFromUser: boolean;
  sender: string;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}
```

### IConversation
```typescript
interface IConversation {
  id: string;
  contactName: string;
  contactNumber: string;
  lastMessage: string;
  lastMessageTime: Date;
  unreadCount: number;
  avatar?: string;
}
```

## Funcionalidades Implementadas

### ✅ Concluído
- [x] Layout com dois containers (principal e secundário)
- [x] Lista de conversas com busca
- [x] Área de mensagens com scroll invertido
- [x] Auto-scroll para novas mensagens
- [x] Input de mensagem com validação
- [x] Estados de loading e erro
- [x] Indicadores de status das mensagens
- [x] Formatação de datas em português
- [x] Design responsivo
- [x] Integração com API de envio de mensagens

### 🔄 Dados Mockados (Para Implementação Futura)
- [ ] API para buscar conversas reais
- [ ] API para buscar mensagens reais
- [ ] WebSocket para mensagens em tempo real
- [ ] Notificações de novas mensagens

## Como Usar

1. **Na página de conversação:**
```tsx
import { ChatInterface } from '@/components/organisms';

function ConversationPage() {
  return (
    <div className="h-screen">
      <ChatInterface />
    </div>
  );
}
```

2. **O componente automaticamente:**
   - Busca o agente pelo ID da URL
   - Carrega as conversas disponíveis
   - Permite selecionar e visualizar mensagens
   - Permite enviar novas mensagens

## Dependências

- `date-fns`: Formatação de datas
- `lucide-react`: Ícones
- `@radix-ui/react-scroll-area`: Componente de scroll
- `sonner`: Notificações toast

## Estilização

O componente utiliza:
- **Tailwind CSS** para estilização
- **Componentes shadcn/ui** para elementos base
- **Design system** consistente com o resto da aplicação
- **Cores personalizadas** definidas no CSS global

## Navegação

O componente é acessado através da rota:
```
/app/chats/conversation/:id
```

Onde `:id` é o ID do agente selecionado.
