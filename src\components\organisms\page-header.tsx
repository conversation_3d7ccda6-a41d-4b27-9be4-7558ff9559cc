import type { PageHeaderProps } from "@/interfaces/IPageHeader";


function PageHeader({ title, description, icon }: PageHeaderProps) {
    return (
        <header className="mb-6">
            <section className="flex items-center space-x-2">
                {icon && <div className="mb-2">{icon}</div>}
                <h1 className="text-3xl font-bold text-gray-900">
                    {title}
                </h1>
            </section>
                {description && (
                <p className="mt-2 text-base text-gray-500">
                    {description}
                </p>
            )}
            <hr className="mt-4 border-gray-200" />
        </header>
    );
}

export default PageHeader;
