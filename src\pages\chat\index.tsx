import { Dialog, DialogContent } from '@/components';
import { AgentCardList } from '@/components/organisms/agents/agent-card';
import LoaderComponent from '@/components/organisms/loader';
import NotFoundData from '@/components/organisms/not-found-data';
import PageHeader from '@/components/organisms/page-header';
import useChat from '@/hooks/useChat';
import { ChartGanttIcon } from 'lucide-react';

function ChatPage() {
  const {
    loading,
    agents,
    handleSelectAgent,
    qrCodeUrlImage,
    dialogOpen,
    handleModal,
  } = useChat();
  return (
    <main className="container mx-auto flex flex-col">
      <PageHeader
        title="Chat"
        description="Gerencie o chat dos seus agentes de forma eficaz."
        icon={<ChartGanttIcon size={30} className="text-blue-500" />}
      />
      <section>
        <div className="p-4">
          <h2 className="text-xl font-semibold mb-4">Seus Agentes</h2>
          {loading ? (
            <LoaderComponent />
          ) : agents.length > 0 ? (
            <>
              <AgentCardList
                agents={agents}
                onEdit={(agent) => console.log('Editar agente:', agent)}
                onDelete={(agent) => console.log('Excluir agente:', agent)}
                showActions={false}
                onSelect={handleSelectAgent}
              />
              <Dialog open={dialogOpen} onOpenChange={handleModal}>
                <DialogContent>
                  <div className="flex flex-col items-center p-6">
                    <h2 className="text-xl font-bold mb-2 text-center">QR Code</h2>
                    <span className="mb-4 text-gray-600 text-center">
                      Aponte a câmera do seu celular para o QR Code abaixo:
                    </span>
                    <div className="flex justify-center items-center w-full min-h-[180px]">
                      {qrCodeUrlImage !== null  ? (
                        <img
                          src={qrCodeUrlImage}
                          alt="QR Code"
                          className="rounded-lg shadow-md w-40 h-40 object-contain"
                        />
                      ) : (
                        <p className="text-gray-500">Carregando QR Code...</p>
                      )}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </>
          ) : (
            <NotFoundData message="Nenhum agente encontrado. Crie um novo agente para começar." />
          )}
        </div>
      </section>
    </main>
  );
}

export default ChatPage;
