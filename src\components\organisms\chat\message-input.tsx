import React from 'react';
import { Button } from '@/components';
import { Textarea } from '@/components/ui/textarea';
import { SendIcon, LoaderIcon } from 'lucide-react';

interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  disabled?: boolean;
  loading?: boolean;
  placeholder?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChange,
  onSend,
  disabled = false,
  loading = false,
  placeholder = "Digite sua mensagem...",
}) => {
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!disabled && !loading && value.trim()) {
        onSend();
      }
    }
  };

  const handleSend = () => {
    if (!disabled && !loading && value.trim()) {
      onSend();
    }
  };

  return (
    <div className="bg-white border-t border-gray-200 p-4">
      <div className="flex gap-3 items-end">
        <div className="flex-1">
          <Textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled || loading}
            className="min-h-[44px] max-h-32 resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            rows={1}
          />
        </div>
        
        <Button
          onClick={handleSend}
          disabled={disabled || loading || !value.trim()}
          size="sm"
          className="h-11 px-4 bg-blue-500 hover:bg-blue-600 text-white"
        >
          {loading ? (
            <LoaderIcon size={18} className="animate-spin" />
          ) : (
            <SendIcon size={18} />
          )}
        </Button>
      </div>
      
      <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
        <span>
          Pressione Enter para enviar, Shift+Enter para nova linha
        </span>
        <span className={`${value.length > 1000 ? 'text-red-500' : ''}`}>
          {value.length}/1000
        </span>
      </div>
    </div>
  );
};

export default MessageInput;
