import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTrigger,
} from '@/components';
import type { FieldConfig } from '@/interfaces/ICustomForm';
import type { CreateAgentInput } from '@/schemas/agents-schema';
import { Plus } from 'lucide-react';
import type { UseFormReturn } from 'react-hook-form';
import { CustomForm } from '../custom-form';

interface Props {
  form: UseFormReturn<CreateAgentInput>;
  inputs: FieldConfig[];
  handleSubmit: (data: CreateAgentInput) => void;
  dialogIsOpen?: boolean;
  setDialogIsOpen?: (isOpen: boolean) => void;
  type: 'create' | 'edit';
  initialSearchLabel?: string;
}

export function AgentDialog({
  form,
  inputs,
  handleSubmit,
  dialogIsOpen,
  setDialogIsOpen,
  type,
  initialSearchLabel
}: Props) {
  return (
    <Dialog open={dialogIsOpen} onOpenChange={setDialogIsOpen}>
      {type === 'create' && (
        <DialogTrigger asChild>
          <Button
            className="w-fit flex items-center gap-2 font-semibold px-4 h-10 py-2 rounded shadow transition-colors cursor-pointer"
            size="lg"
          >
            <Plus size={20} />
            {type === 'create' ? 'Novo Agente' : 'Editar Agente'}
          </Button>
        </DialogTrigger>
      )}
      <DialogContent>
        <DialogHeader>
          <h2 className="text-2xl font-bold">
            {type === 'create' ? 'Criar Novo Agente' : 'Editar Agente'}
          </h2>
          <p className="text-muted-foreground mt-2">
            Preencha o formulário abaixo para{' '}
            {type === 'create'
              ? 'criar um novo agente.'
              : 'editar o agente selecionado.'}
          </p>
        </DialogHeader>
        <div>
          <CustomForm<CreateAgentInput>
            form={form}
            fields={inputs}
            onSubmit={handleSubmit}
            initialSearchLabel={initialSearchLabel}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
