/* eslint-disable @typescript-eslint/no-explicit-any */
import type { FieldValues, UseFormReturn } from "react-hook-form";

// Tipos suportados no formulário
type FieldType = "text" | "number" | "textarea" | "select" | "search" | string;

// Definição de cada campo do formulário
// --- FieldConfig atualizado ---
interface FieldConfig<T = any> {
  name: string;
  label: string;
  placeholder?: string;
  type: FieldType;
  options?: { label: string; value: string }[];
  wrapperClassName?: string;
  inputClassName?: string;
  // só obrigatórios para type === "search"
  searchFn?: (query: string) => Promise<T[]>;
  getLabel?: (item: T) => string;
  debounceMs?: number;
  initialLabel?: string;
}


interface CustomFormProps<T extends FieldValues = FieldValues> {
  fields: FieldConfig<T>[];
  onSubmit: (value: any) => void;
  submitLabel?: string;
  form: UseFormReturn<T>;
  formClassName?: string;
  initialSearchLabel?: string;
};

export type {
  CustomFormProps, FieldConfig,
  FieldType
};

