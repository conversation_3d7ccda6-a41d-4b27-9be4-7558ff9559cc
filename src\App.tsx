import { ThemeProvider } from './components';
import { Router } from './routes';
import { Toaster } from 'sonner';
import { AuthProvider } from './contexts/auth-context';

const App = () => {
  return (
    <ThemeProvider defaultTheme="light" storageKey="@hc/color-theme">
      <AuthProvider>
        <Toaster richColors position="top-right" />
        <Router />
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
