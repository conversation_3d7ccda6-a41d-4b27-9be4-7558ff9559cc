interface GenerateQrCodeInputInterface {
  instanceName: string;
}

interface SendMessageInputInterface {
  delay: number;
  number: string;
  text: string;
}

interface CheckConnectionStatusOutputInterface {
  success: boolean;
  isConnected: boolean;
  error: string | null;
  rawResponse: string;
}

interface GenerateQrCodeOutputInterface {
  success?: boolean;
  base64?: string | null;
  code?: string | null;
  error?: string | null;
  rawResponse?: string;
  message?: string | null;
  status?: string | null;
  connected?: boolean | null;
  pairingCode?: string | null;
}

// Interfaces para mensagens e conversas
interface IMessage {
  id: number;
  role: string;
  messageIdentificator: string;
  agentId: number;
  content: string;
  timestamp: string;
}

interface IConversation {
  agentId: number;
  username: string;
  conversationIndetification: string;
}

interface IGetMessagesInputInterface {
  instanceName: string;
  contactNumber: string;
  limit?: number;
  offset?: number;
}

interface IGetConversationsInputInterface {
  instanceName: string;
  limit?: number;
  offset?: number;
}

export type {
  CheckConnectionStatusOutputInterface,
  GenerateQrCodeInputInterface,
  GenerateQrCodeOutputInterface,
  SendMessageInputInterface,
  IMessage,
  IConversation,
  IGetMessagesInputInterface,
  IGetConversationsInputInterface,
};
